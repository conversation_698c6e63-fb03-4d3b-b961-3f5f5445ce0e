# Flow2Pay Direct Provider

A direct integration with Flow2Pay's PIX API that eliminates the intermediate Pluggou PIX layer for improved performance and reduced latency.

## Overview

This provider implements a direct connection to Flow2Pay's REST API, providing:

- **PIX QR Code Generation** - Create dynamic QR codes for receiving payments
- **PIX Transfers** - Send money via PIX to any PIX key
- **Transaction Status** - Query the status of payments and transfers
- **PIX Refunds** - Process refunds for received payments
- **Comprehensive Error Handling** - Robust retry logic and error mapping
- **Performance Optimized** - Direct API calls without intermediate layers

## Architecture Benefits

### Before (Pluggou PIX)
```
Your App → Pluggou PIX API (encore.dev) → Flow2Pay API
```

### After (Direct Flow2Pay)
```
Your App → Flow2Pay API (direct)
```

**Performance Improvements:**
- Eliminates one network hop
- Reduces response times by ~200-500ms
- Removes dependency on intermediate service
- Direct error handling and retry logic

## Configuration

### 1. Database Setup

Create a Flow2Pay gateway in your `payment_gateways` table:

```sql
INSERT INTO payment_gateways (
  id, name, type, is_active, can_receive, can_send, is_global, 
  credentials, created_at, updated_at
) VALUES (
  'flow2pay-gateway-id',
  'Flow2Pay Direct',
  'FLOW2PAY',
  true,
  true,
  true,
  true,
  '{
    "clientId": "your_flow2pay_client_id",
    "clientSecret": "your_flow2pay_client_secret", 
    "eventToken": "your_flow2pay_event_token",
    "apiUrl": "https://pixv2.flow2pay.com.br"
  }',
  NOW(),
  NOW()
);
```

### 2. Organization Association

Associate the gateway with your organization:

```sql
INSERT INTO organization_gateways (
  organization_id, gateway_id, is_active, is_default, priority
) VALUES (
  'your-organization-id',
  'flow2pay-gateway-id', 
  true,
  true,
  1
);
```

### 3. Environment Variables (Fallback)

If not configured in the database, the provider will use environment variables:

```env
FLOW2PAY_CLIENT_ID=your_client_id
FLOW2PAY_CLIENT_SECRET=your_client_secret
FLOW2PAY_EVENT_TOKEN=your_event_token
FLOW2PAY_API_URL=https://pixv2.flow2pay.com.br
```

## Usage

### Creating PIX QR Code Payments

```typescript
import { flow2pay } from "@repo/payments/provider";

const result = await flow2pay.createPixPayment({
  amount: 100.50,
  customerName: "João Silva",
  customerEmail: "<EMAIL>",
  customerDocument: "12345678901", // Optional: restricts payment to this CPF
  description: "Pagamento de produto",
  organizationId: "org-123",
  metadata: {
    orderId: "order-456",
    productId: "prod-789"
  }
});

console.log("QR Code:", result.qrCode);
console.log("Transaction ID:", result.externalId);
console.log("Expiration:", result.expiration);
```

### Processing PIX Transfers

```typescript
const result = await flow2pay.processPixWithdrawal({
  amount: 50.00,
  pixKey: "<EMAIL>",
  pixKeyType: "EMAIL",
  organizationId: "org-123",
  description: "Transferência para cliente",
  metadata: {
    transferReason: "refund",
    originalOrderId: "order-456"
  }
});

console.log("Transfer ID:", result.idEnvio);
console.log("Status:", result.status);
```

### Checking Transaction Status

```typescript
const result = await flow2pay.getTransactionStatus({
  transactionId: "transaction-id-or-external-id",
  organizationId: "org-123",
  transactionType: "CHARGE" // or "SEND"
});

console.log("Status:", result.status);
console.log("Amount:", result.amount);
```

### Processing Refunds

```typescript
const result = await flow2pay.processRefund({
  transactionId: "original-transaction-id",
  amount: 100.50,
  reason: "Produto defeituoso",
  organizationId: "org-123",
  endToEndId: "E12345678901234567890123456789012" // Optional
});

console.log("Refund Status:", result.status);
```

## API Reference

### Flow2Pay Endpoints Used

| Operation | Endpoint | Method | Description |
|-----------|----------|---------|-------------|
| Authentication | `/no-auth/autenticacao/v1/api/login` | POST | Get access token |
| Create QR Code | `/qrcode/v2/gerar` | POST | Generate PIX QR code |
| Transfer PIX | `/pix/v1/transferir` | POST | Send PIX transfer |
| Transaction Status | `/transacao/v1/buscar` | GET | Query transaction |
| Refund | `/estorno/v1/pix-in` | POST | Process refund |

### Response Format

All provider methods return a standardized response:

```typescript
interface ProviderResponse {
  success: boolean;
  transactionId?: string;
  externalId: string;
  status: string;
  amount?: number;
  message?: string;
  metadata?: Record<string, any>;
  raw?: any; // Original Flow2Pay response
}
```

## Error Handling

The provider includes comprehensive error handling:

- **Authentication Errors**: Automatic token refresh
- **Network Errors**: Exponential backoff retry logic
- **Rate Limiting**: Automatic retry with delays
- **Business Errors**: Detailed error mapping from Flow2Pay responses

## Testing

Run the test script to verify your configuration:

```bash
npx ts-node scripts/test-flow2pay-provider.ts your-organization-id
```

## Migration from Pluggou PIX

To migrate from the existing Pluggou PIX implementation:

1. **Configure Flow2Pay credentials** as shown above
2. **Update gateway type** in your organization settings from `PLUGGOU_PIX` to `FLOW2PAY`
3. **Test thoroughly** in your staging environment
4. **Monitor performance** - you should see reduced response times

## Performance Monitoring

The provider includes detailed logging for performance monitoring:

```typescript
// Response times are logged automatically
logger.info("Flow2Pay API call successful", {
  endpoint: "/qrcode/v2/gerar",
  organizationId: "org-123",
  responseTime: "1.2s"
});
```

## Security

- **Credential Management**: Secure storage in database with encryption
- **Token Caching**: Access tokens cached securely with automatic refresh
- **Request Validation**: All inputs validated before API calls
- **Error Sanitization**: Sensitive data removed from error logs

## Support

For issues with this provider:

1. Check the logs for detailed error information
2. Verify your Flow2Pay credentials are correct
3. Test with the provided test script
4. Contact Flow2Pay support for API-specific issues
